[2025-07-26 00:05:20] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-26 00:05:29] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:05:29] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 00:30:00] local.ERROR: syntax error, unexpected token "=", expecting "]" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"=\", expecting \"]\" at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php:725)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(149): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Filesystem\\Filesystem->requireOnce('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\Helper.php(27): Illuminate\\Support\\Facades\\Facade::__callStatic('requireOnce', Array)
#3 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(72): Xmetr\\Base\\Supports\\Helper::autoload('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(39): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->registerAutoloadPathFromTheme('xmetr')
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->boot()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider))
#12 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider), 113)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#18 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-07-26 00:30:00] local.ERROR: syntax error, unexpected token "=", expecting "]" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"=\", expecting \"]\" at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php:725)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(149): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Filesystem\\Filesystem->requireOnce('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\Helper.php(27): Illuminate\\Support\\Facades\\Facade::__callStatic('requireOnce', Array)
#3 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(72): Xmetr\\Base\\Supports\\Helper::autoload('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\ThemeManagementServiceProvider.php(39): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->registerAutoloadPathFromTheme('xmetr')
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider->boot()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider))
#12 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Xmetr\\Theme\\Providers\\ThemeManagementServiceProvider), 113)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#18 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-07-26 00:53:42] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select `name`, `id` from `pages` where `status` = published) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select `name`, `id` from `pages` where `status` = published) at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select `name`, ...', Array, Object(Closure))
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select `name`, ...', Array, Object(Closure))
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3142): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3138): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(889): Illuminate\\Database\\Query\\Builder->pluck('name', 'id')
#9 D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php(562): Illuminate\\Database\\Eloquent\\Builder->pluck('name', 'id')
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Xmetr\\RealEstate\\Providers\\HookServiceProvider->Xmetr\\RealEstate\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1045): Illuminate\\Foundation\\Application->fireAppCallbacks(Array)
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#16 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', '', Array)
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `name`, ...', Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select `name`, ...', Array, Object(Closure))
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select `name`, ...', Array, Object(Closure))
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select `name`, ...', Array, Object(Closure))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `name`, ...', Array, Object(Closure))
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select `name`, ...', Array, true)
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3142): Illuminate\\Database\\Query\\Builder->runSelect()
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3138): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(889): Illuminate\\Database\\Query\\Builder->pluck('name', 'id')
#20 D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php(562): Illuminate\\Database\\Eloquent\\Builder->pluck('name', 'id')
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Xmetr\\RealEstate\\Providers\\HookServiceProvider->Xmetr\\RealEstate\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1045): Illuminate\\Foundation\\Application->fireAppCallbacks(Array)
#23 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}
"} 
[2025-07-26 16:01:08] local.WARNING: DB query exceeded 559.85 ms. SQL: select * from `menus` where `status` = ?  
[2025-07-26 16:26:08] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\PropertyStatusEnum  
[2025-07-26 16:26:26] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:26:43] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:26:45] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:27:40] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:33:02] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:54:39] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 16:55:38] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
[2025-07-26 18:27:08] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-26 18:27:14] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:27:14] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-26 18:48:10] local.ERROR: Class Xmetr\RealEstate\Repositories\Eloquent\PropertyRepository contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface::getPropertiesCount) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Class Xmetr\\RealEstate\\Repositories\\Eloquent\\PropertyRepository contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (Xmetr\\RealEstate\\Repositories\\Interfaces\\PropertyInterface::getPropertiesCount) at D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php:19)
[stacktrace]
#0 {main}
"} 
[2025-07-26 19:17:29] local.ERROR: require(D:\laragon\www\xmetr\D:\laragon\www\xmetr\bootstrap\cache/routes-v7_en.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): require(D:\\laragon\\www\\xmetr\\D:\\laragon\\www\\xmetr\\bootstrap\\cache/routes-v7_en.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:108)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 108)
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(108): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'require(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 108)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(108): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1045): Illuminate\\Foundation\\Application->fireAppCallbacks(Array)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#10 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#11 {main}
"} 
[2025-07-26 19:18:04] local.ERROR: require(D:\laragon\www\xmetr\bootstrap\cache\routes-v7.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): require(D:\\laragon\\www\\xmetr\\bootstrap\\cache\\routes-v7.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php:108)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 108)
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(108): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'require(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 108)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(108): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1045): Illuminate\\Foundation\\Application->fireAppCallbacks(Array)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#9 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 {main}
"} 
[2025-07-26 19:48:40] local.ERROR: Translation file [D:\laragon\www\xmetr\platform\themes\xmetr/lang/en.json] contains an invalid JSON structure. {"userId":1,"exception":"[object] (RuntimeException(code: 0): Translation file [D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr/lang/en.json] contains an invalid JSON structure. at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\FileLoader.php:152)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(791): Illuminate\\Translation\\FileLoader->Illuminate\\Translation\\{closure}(Array, 'D:\\\\laragon\\\\www\\\\...', 0)
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\FileLoader.php(147): Illuminate\\Support\\Collection->reduce(Object(Closure), Array)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\FileLoader.php(64): Illuminate\\Translation\\FileLoader->loadJsonPaths('en')
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\Translator.php(311): Illuminate\\Translation\\FileLoader->load('en', '*', '*')
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\Translator.php(148): Illuminate\\Translation\\Translator->load('*', '*', 'en')
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(923): Illuminate\\Translation\\Translator->get('Home', Array, 'en')
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(958): trans('Home', Array, NULL)
#7 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(358): __('Home')
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Theme\\Theme->breadcrumb()
#9 D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Services\\PageService.php(52): Illuminate\\Support\\Facades\\Facade::__callStatic('breadcrumb', Array)
#10 D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php(142): Xmetr\\Page\\Services\\PageService->handleFrontRoutes(Object(Xmetr\\Slug\\Models\\Slug))
#11 [internal function]: Xmetr\\Page\\Providers\\HookServiceProvider->handleSingleView(Object(Xmetr\\Slug\\Models\\Slug))
#12 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Base\\Supports\\Filter->fire('filter_public_s...', Array)
#14 D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#15 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(62): apply_filters('filter_public_s...', Object(Xmetr\\Slug\\Models\\Slug))
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Xmetr\\Theme\\Http\\Controllers\\PublicController->getView('rent-properties', '')
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getView', Array)
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\Theme\\Http\\Controllers\\PublicController), 'getView')
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#51 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#60 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#83 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#85 {main}
"} 
[2025-07-26 20:15:09] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\RentalPeriodEnum  
